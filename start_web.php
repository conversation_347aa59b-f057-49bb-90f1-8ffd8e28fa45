<?php
/**
 * Web服务器启动脚本
 * 
 * 为照顾重病父母的程序员提供便捷的Web服务启动
 * 
 * <AUTHOR> (照顾重病父母中)
 * @version 1.0
 */

echo "=== 支付宝数据同步系统 Web服务器 ===\n";
echo "作者: 程序员 (照顾重病父母中)\n";
echo "版本: 1.0\n";
echo "=====================================\n\n";

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.4.0', '<')) {
    echo "❌ PHP版本过低，需要PHP 7.4或更高版本\n";
    echo "当前版本: " . PHP_VERSION . "\n";
    exit(1);
}

// 检查web目录
$webDir = __DIR__ . '/web';
if (!is_dir($webDir)) {
    echo "❌ Web目录不存在: {$webDir}\n";
    exit(1);
}

// 检查必要文件
$requiredFiles = [
    $webDir . '/index.php',
    $webDir . '/data.php'
];

foreach ($requiredFiles as $file) {
    if (!file_exists($file)) {
        echo "❌ 必要文件不存在: {$file}\n";
        exit(1);
    }
}

// 检查数据库配置
$configFile = __DIR__ . '/config/database.php';
if (!file_exists($configFile)) {
    echo "❌ 数据库配置文件不存在: {$configFile}\n";
    echo "请先复制 config/database.example.php 为 config/database.php 并配置数据库信息\n";
    exit(1);
}

// 检查vendor目录
$vendorDir = __DIR__ . '/vendor';
if (!is_dir($vendorDir)) {
    echo "❌ 依赖包目录不存在，请先运行: composer install\n";
    exit(1);
}

// 获取可用端口
$defaultPort = 8080;
$port = $defaultPort;

// 检查端口是否被占用
for ($i = 0; $i < 10; $i++) {
    $testPort = $defaultPort + $i;
    $connection = @fsockopen('127.0.0.1', $testPort, $errno, $errstr, 1);
    if (!$connection) {
        $port = $testPort;
        break;
    } else {
        fclose($connection);
    }
}

echo "✅ 环境检查通过\n";
echo "📁 Web目录: {$webDir}\n";
echo "🌐 启动端口: {$port}\n";
echo "🔗 访问地址: http://localhost:{$port}\n\n";

echo "正在启动Web服务器...\n";
echo "按 Ctrl+C 停止服务器\n";
echo "=====================================\n\n";

// 启动内置Web服务器
$command = sprintf(
    'php -S localhost:%d -t %s',
    $port,
    escapeshellarg($webDir)
);

// 在后台打开浏览器（可选）
if (PHP_OS_FAMILY === 'Darwin') {
    // macOS
    exec("sleep 2 && open http://localhost:{$port} > /dev/null 2>&1 &");
} elseif (PHP_OS_FAMILY === 'Windows') {
    // Windows
    exec("timeout 2 > nul && start http://localhost:{$port}");
} elseif (PHP_OS_FAMILY === 'Linux') {
    // Linux
    exec("sleep 2 && xdg-open http://localhost:{$port} > /dev/null 2>&1 &");
}

// 执行命令
passthru($command);
