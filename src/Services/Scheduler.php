<?php

namespace AlipayBillQuery\Services;

use Exception;

/**
 * 定时任务调度器
 */
class Scheduler
{
    private $dataService;
    private $appId;
    private $privateKey;
    private $alipayPublicKey;
    private $logFile;

    public function __construct($appId, $privateKey, $alipayPublicKey)
    {
        $this->appId = $appId;
        $this->privateKey = $privateKey;
        $this->alipayPublicKey = $alipayPublicKey;
        $this->dataService = new DataService();
        $this->logFile = __DIR__ . '/../../logs/scheduler.log';
        
        // 确保日志目录存在
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    /**
     * 执行定时任务
     */
    public function run()
    {
        $this->log("定时任务开始执行");
        
        try {
            // 检查系统状态
            if (!$this->dataService->checkSystemStatus()) {
                $this->log("系统状态检查失败，跳过本次同步", 'ERROR');
                return false;
            }
            
            // 执行智能增量同步
            $success = $this->dataService->performIncrementalSync(
                $this->appId,
                $this->privateKey,
                $this->alipayPublicKey,
                60 // 回退策略：如果没有历史数据，获取最近60分钟的数据
            );
            
            if ($success) {
                $this->log("增量同步执行成功");
            } else {
                $this->log("增量同步执行失败", 'ERROR');
            }
            
            // 每小时清理一次旧日志（当分钟数为0时）
            if (date('i') == '00') {
                $this->dataService->cleanOldSyncLogs(30);
                $this->log("清理旧同步日志完成");
            }
            
            return $success;
            
        } catch (Exception $e) {
            $this->log("定时任务执行异常: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }

    /**
     * 执行初始化同步
     */
    public function runInitialSync($startDate = '2024-01-01', $endDate = '2025-05-31')
    {
        $this->log("开始执行初始化同步: {$startDate} 到 {$endDate}");
        
        try {
            $success = $this->dataService->performInitialSync(
                $this->appId,
                $this->privateKey,
                $this->alipayPublicKey,
                $startDate,
                $endDate
            );
            
            if ($success) {
                $this->log("初始化同步执行成功");
            } else {
                $this->log("初始化同步执行失败", 'ERROR');
            }
            
            return $success;
            
        } catch (Exception $e) {
            $this->log("初始化同步执行异常: " . $e->getMessage(), 'ERROR');
            return false;
        }
    }

    /**
     * 获取统计信息
     */
    public function getStatistics()
    {
        $this->log("获取统计信息");
        return $this->dataService->getDataStatistics();
    }

    /**
     * 获取同步日志
     */
    public function getSyncLogs($limit = 10)
    {
        return $this->dataService->getSyncLogs($limit);
    }

    /**
     * 记录日志
     */
    private function log($message, $level = 'INFO')
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        // 输出到控制台
        echo $logMessage;
        
        // 写入日志文件
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
    }

    /**
     * 生成crontab配置
     */
    public static function generateCrontabConfig($projectPath)
    {
        $cronJob = "# 支付宝数据同步定时任务 - 每分钟执行一次\n";
        $cronJob .= "* * * * * cd {$projectPath} && /usr/bin/php cron_job.php >> logs/cron.log 2>&1\n";
        $cronJob .= "\n";
        $cronJob .= "# 备用配置 - 如果上面的路径不正确，请修改php路径\n";
        $cronJob .= "# * * * * * cd {$projectPath} && php cron_job.php >> logs/cron.log 2>&1\n";
        
        return $cronJob;
    }

    /**
     * 安装crontab任务
     */
    public static function installCrontab($projectPath)
    {
        echo "=== 安装定时任务 ===\n";
        
        $cronConfig = self::generateCrontabConfig($projectPath);
        $tempFile = tempnam(sys_get_temp_dir(), 'zfb_cron');
        
        // 获取现有的crontab
        $existingCron = shell_exec('crontab -l 2>/dev/null') ?: '';
        
        // 检查是否已经存在相同的任务
        if (strpos($existingCron, 'cron_job.php') !== false) {
            echo "⚠️  检测到已存在的定时任务，请手动检查crontab配置\n";
            echo "运行 'crontab -l' 查看现有任务\n";
            echo "运行 'crontab -e' 编辑任务\n";
            return false;
        }
        
        // 写入新的crontab配置
        file_put_contents($tempFile, $existingCron . $cronConfig);
        
        // 安装crontab
        $result = shell_exec("crontab {$tempFile} 2>&1");
        unlink($tempFile);
        
        if ($result === null || empty(trim($result))) {
            echo "✅ 定时任务安装成功！\n";
            echo "任务配置:\n";
            echo $cronConfig;
            echo "\n";
            echo "可以运行 'crontab -l' 查看已安装的任务\n";
            return true;
        } else {
            echo "❌ 定时任务安装失败: {$result}\n";
            echo "请手动添加以下配置到crontab:\n";
            echo $cronConfig;
            return false;
        }
    }

    /**
     * 卸载crontab任务
     */
    public static function uninstallCrontab()
    {
        echo "=== 卸载定时任务 ===\n";
        
        // 获取现有的crontab
        $existingCron = shell_exec('crontab -l 2>/dev/null') ?: '';
        
        if (empty($existingCron)) {
            echo "没有找到现有的定时任务\n";
            return true;
        }
        
        // 移除包含cron_job.php的行
        $lines = explode("\n", $existingCron);
        $newLines = [];
        $removed = false;
        
        foreach ($lines as $line) {
            if (strpos($line, 'cron_job.php') === false) {
                $newLines[] = $line;
            } else {
                $removed = true;
                echo "移除任务: " . trim($line) . "\n";
            }
        }
        
        if (!$removed) {
            echo "没有找到相关的定时任务\n";
            return true;
        }
        
        // 更新crontab
        $newCron = implode("\n", $newLines);
        $tempFile = tempnam(sys_get_temp_dir(), 'zfb_cron');
        file_put_contents($tempFile, $newCron);
        
        $result = shell_exec("crontab {$tempFile} 2>&1");
        unlink($tempFile);
        
        if ($result === null || empty(trim($result))) {
            echo "✅ 定时任务卸载成功！\n";
            return true;
        } else {
            echo "❌ 定时任务卸载失败: {$result}\n";
            return false;
        }
    }

    /**
     * 检查定时任务状态
     */
    public static function checkCrontabStatus()
    {
        echo "=== 定时任务状态 ===\n";
        
        $existingCron = shell_exec('crontab -l 2>/dev/null') ?: '';
        
        if (empty($existingCron)) {
            echo "❌ 没有找到任何定时任务\n";
            return false;
        }
        
        $lines = explode("\n", $existingCron);
        $found = false;
        
        foreach ($lines as $line) {
            if (strpos($line, 'cron_job.php') !== false) {
                echo "✅ 找到定时任务: " . trim($line) . "\n";
                $found = true;
            }
        }
        
        if (!$found) {
            echo "❌ 没有找到支付宝数据同步的定时任务\n";
        }
        
        echo "==================\n";
        
        return $found;
    }
}
