{"name": "alipay/data-sync-system", "description": "支付宝数据同步系统 - 自动化的支付宝交易数据获取和存储系统", "type": "project", "keywords": ["alipay", "data", "sync", "automation", "mysql", "cron"], "license": "MIT", "authors": [{"name": "程序员 (照顾重病父母中)", "email": "<EMAIL>", "role": "Developer"}], "require": {"php": ">=7.4", "alipaysdk/easysdk": "^2.2", "ext-pdo": "*", "ext-json": "*"}, "autoload": {"psr-4": {"AlipayBillQuery\\": "src/"}}, "scripts": {"install": "php install.php", "sync": "php cron_job.php", "test": "php test_system.php", "web": "php start_web.php"}, "config": {"optimize-autoloader": true, "sort-packages": true}, "minimum-stability": "stable", "prefer-stable": true}