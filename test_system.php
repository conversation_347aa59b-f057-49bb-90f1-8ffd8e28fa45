<?php
/**
 * 系统测试脚本
 * 
 * 用于测试数据库连接、表结构和基本功能
 */

require_once 'vendor/autoload.php';

use AlipayBillQuery\Database\Connection;
use AlipayBillQuery\Database\Migration;
use AlipayBillQuery\Services\DataService;

function testDatabaseConnection()
{
    echo "=== 测试数据库连接 ===\n";
    
    try {
        $db = Connection::getInstance();
        $result = $db->query("SELECT 1 as test")->fetch();
        
        if ($result['test'] == 1) {
            echo "✅ 数据库连接成功\n";
            return true;
        } else {
            echo "❌ 数据库连接异常\n";
            return false;
        }
    } catch (Exception $e) {
        echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
        return false;
    }
}

function testTableStructure()
{
    echo "\n=== 测试表结构 ===\n";
    
    try {
        $db = Connection::getInstance();
        $tables = ['25_zfb_data', 'zfb_sync_log'];
        $allExists = true;
        
        foreach ($tables as $table) {
            if ($db->tableExists($table)) {
                echo "✅ 表 {$table} 存在\n";
                
                // 获取表结构信息
                $sql = "DESCRIBE `{$table}`";
                $columns = $db->query($sql)->fetchAll();
                echo "   字段数: " . count($columns) . "\n";
                
                // 获取记录数
                $sql = "SELECT COUNT(*) as count FROM `{$table}`";
                $result = $db->query($sql)->fetch();
                echo "   记录数: " . number_format($result['count']) . "\n";
                
            } else {
                echo "❌ 表 {$table} 不存在\n";
                $allExists = false;
            }
        }
        
        return $allExists;
        
    } catch (Exception $e) {
        echo "❌ 表结构检查失败: " . $e->getMessage() . "\n";
        return false;
    }
}

function testDataOperations()
{
    echo "\n=== 测试数据操作 ===\n";
    
    try {
        $db = Connection::getInstance();
        
        // 测试插入操作
        $testData = [
            'alipay_order_no' => 'TEST_' . time(),
            'gmt_create' => date('Y-m-d H:i:s'),
            'goods_title' => '测试商品',
            'total_amount' => 0.01,
            'trade_status' => '测试',
            'data_source' => 'test'
        ];
        
        $sql = "INSERT INTO `25_zfb_data` 
                (`alipay_order_no`, `gmt_create`, `goods_title`, `total_amount`, `trade_status`, `data_source`) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $id = $db->insert($sql, [
            $testData['alipay_order_no'],
            $testData['gmt_create'],
            $testData['goods_title'],
            $testData['total_amount'],
            $testData['trade_status'],
            $testData['data_source']
        ]);
        
        if ($id > 0) {
            echo "✅ 数据插入成功，ID: {$id}\n";
            
            // 测试查询操作
            $sql = "SELECT * FROM `25_zfb_data` WHERE `id` = ?";
            $result = $db->query($sql, [$id])->fetch();
            
            if ($result && $result['alipay_order_no'] == $testData['alipay_order_no']) {
                echo "✅ 数据查询成功\n";
                
                // 测试更新操作
                $sql = "UPDATE `25_zfb_data` SET `goods_title` = ? WHERE `id` = ?";
                $affectedRows = $db->update($sql, ['测试商品(已更新)', $id]);
                
                if ($affectedRows > 0) {
                    echo "✅ 数据更新成功\n";
                } else {
                    echo "❌ 数据更新失败\n";
                }
                
                // 清理测试数据
                $sql = "DELETE FROM `25_zfb_data` WHERE `id` = ?";
                $db->update($sql, [$id]);
                echo "✅ 测试数据清理完成\n";
                
                return true;
            } else {
                echo "❌ 数据查询失败\n";
                return false;
            }
        } else {
            echo "❌ 数据插入失败\n";
            return false;
        }
        
    } catch (Exception $e) {
        echo "❌ 数据操作测试失败: " . $e->getMessage() . "\n";
        return false;
    }
}

function testSystemServices()
{
    echo "\n=== 测试系统服务 ===\n";
    
    try {
        $dataService = new DataService();
        
        // 测试系统状态检查
        echo "检查系统状态...\n";
        $status = $dataService->checkSystemStatus();
        
        if ($status) {
            echo "✅ 系统服务正常\n";
        } else {
            echo "❌ 系统服务异常\n";
        }
        
        // 测试统计信息获取
        echo "\n获取统计信息...\n";
        $stats = $dataService->getDataStatistics();
        
        return $status;
        
    } catch (Exception $e) {
        echo "❌ 系统服务测试失败: " . $e->getMessage() . "\n";
        return false;
    }
}

function testConfiguration()
{
    echo "\n=== 测试配置文件 ===\n";
    
    // 检查数据库配置
    $dbConfigFile = __DIR__ . '/config/database.php';
    if (file_exists($dbConfigFile)) {
        echo "✅ 数据库配置文件存在\n";
        
        $config = require $dbConfigFile;
        $requiredKeys = ['host', 'username', 'password', 'database', 'charset', 'port'];
        $configValid = true;
        
        foreach ($requiredKeys as $key) {
            if (!isset($config[$key]) || empty($config[$key])) {
                echo "❌ 配置项 {$key} 缺失或为空\n";
                $configValid = false;
            }
        }
        
        if ($configValid) {
            echo "✅ 数据库配置完整\n";
        }
        
        return $configValid;
    } else {
        echo "❌ 数据库配置文件不存在\n";
        return false;
    }
}

function runAllTests()
{
    echo "支付宝数据同步系统 - 系统测试\n";
    echo "================================\n";
    
    $tests = [
        'testConfiguration' => '配置文件测试',
        'testDatabaseConnection' => '数据库连接测试',
        'testTableStructure' => '表结构测试',
        'testDataOperations' => '数据操作测试',
        'testSystemServices' => '系统服务测试'
    ];
    
    $results = [];
    $totalTests = count($tests);
    $passedTests = 0;
    
    foreach ($tests as $testFunction => $testName) {
        echo "\n" . str_repeat("=", 50) . "\n";
        echo "执行测试: {$testName}\n";
        echo str_repeat("=", 50) . "\n";
        
        $result = call_user_func($testFunction);
        $results[$testName] = $result;
        
        if ($result) {
            $passedTests++;
        }
    }
    
    // 显示测试结果汇总
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "测试结果汇总\n";
    echo str_repeat("=", 50) . "\n";
    
    foreach ($results as $testName => $result) {
        $status = $result ? '✅ 通过' : '❌ 失败';
        echo "{$testName}: {$status}\n";
    }
    
    echo str_repeat("-", 50) . "\n";
    echo "总测试数: {$totalTests}\n";
    echo "通过数: {$passedTests}\n";
    echo "失败数: " . ($totalTests - $passedTests) . "\n";
    echo "通过率: " . round(($passedTests / $totalTests) * 100, 2) . "%\n";
    
    if ($passedTests == $totalTests) {
        echo "\n🎉 所有测试通过！系统运行正常。\n";
        return true;
    } else {
        echo "\n⚠️  部分测试失败，请检查相关配置和环境。\n";
        return false;
    }
}

// 运行测试
if (basename(__FILE__) == basename($_SERVER["SCRIPT_NAME"])) {
    runAllTests();
}
